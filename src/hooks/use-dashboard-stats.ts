import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';

interface DashboardStats {
  totalRepositories: number;
  activeReports: number;
  userProfile: number;
  configSettings: number;
}

interface DashboardStatsResponse {
  stats: DashboardStats;
  timestamp: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
}

interface DashboardStatsError {
  error: string;
  message: string;
  timestamp: string;
}

/**
 * Hook to fetch dashboard statistics from the database
 */
export function useDashboardStats() {
  const { data: session } = useSession();

  return useQuery<DashboardStatsResponse, DashboardStatsError>({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/stats');
      const data = await response.json();

      if (!response.ok) {
        const error: DashboardStatsError = {
          error: data.error || 'Failed to fetch dashboard stats',
          message: data.message || `HTTP ${response.status}: ${response.statusText}`,
          timestamp: data.timestamp || new Date().toISOString(),
        };
        throw error;
      }

      return data;
    },
    enabled: !!session?.user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error?.error === 'Not authenticated') {
        return false;
      }
      return failureCount < 3;
    }
  });
}
