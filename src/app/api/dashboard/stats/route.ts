import { getServerSession } from "next-auth/next";
import { authOptions } from "@/src/features/auth/lib/auth-config";
import { NextResponse } from "next/server";
import connectToDatabase from "@/src/lib/database/mongoose";
import { Organization } from "@/src/lib/database/models";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get user's organizations with aggregated data
    const organizations = await Organization.find({
      user_id: session.user.id
    }).select('total_repos installation_status provider repos');

    // Calculate stats for the 4 dashboard cards
    const stats = {
      totalRepositories: 0,
      activeReports: 0, // For future use when we have review/analysis data
      userProfile: 1, // Always 1 for the current user
      configSettings: organizations.length, // Number of connected organizations/providers
    };

    // Aggregate repository data from organizations
    organizations.forEach(org => {
      stats.totalRepositories += org.total_repos || 0;
    });

    return NextResponse.json({
      stats,
      timestamp: new Date().toISOString(),
      user: {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name
      }
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    
    return NextResponse.json({
      error: "Failed to fetch dashboard stats",
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
